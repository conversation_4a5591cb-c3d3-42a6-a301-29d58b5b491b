"""
数据相关路由
"""
from flask import Blueprint, request, jsonify
from datetime import datetime
from services import SpiderService, AnalysisService
from utils.response import success_response, error_response

data_bp = Blueprint('data', __name__)

# 全局变量，在app.py中初始化
spider_service = None
analysis_service = None


def init_data_routes(spider_svc, analysis_svc):
    """初始化路由依赖"""
    global spider_service, analysis_service
    spider_service = spider_svc
    analysis_service = analysis_svc


@data_bp.route('/health')
def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        companies_count = len(analysis_service.db.get_companies()) if analysis_service else 0

        return success_response({
            'status': 'healthy',
            'message': '服务运行正常',
            'timestamp': datetime.now().isoformat(),
            'database': {
                'connected': True,
                'companies_count': companies_count
            }
        })
    except Exception as e:
        return error_response(f'健康检查失败: {str(e)}', status_code=503)


@data_bp.route('/companies')
def get_companies():
    """获取公司列表"""
    try:
        companies = analysis_service.db.get_companies()
        return success_response(companies)
    except Exception as e:
        return error_response(f'获取公司列表失败: {str(e)}')


@data_bp.route('/import_txt', methods=['POST'])
def import_txt_files():
    """导入txt文件到数据库"""
    try:
        data = request.json or {}
        txt_dir = data.get('txt_dir', 'txt')
        result = analysis_service.db.import_txt_files(txt_dir)
        
        # 构建详细的消息
        message_parts = []
        if result['imported'] > 0:
            message_parts.append(f"成功导入 {result['imported']} 个文件")
        if result['skipped'] > 0:
            message_parts.append(f"跳过重复 {result['skipped']} 个文件")
        if result['errors'] > 0:
            message_parts.append(f"失败 {result['errors']} 个文件")
        
        message = "，".join(message_parts) if message_parts else "没有文件需要处理"
        
        return success_response({
            'message': f'导入完成：{message}',
            'imported': result['imported'],
            'skipped': result['skipped'],
            'errors': result['errors'],
            'total': result['imported'] + result['skipped'] + result['errors']
        })
    except Exception as e:
        return error_response(f'导入失败: {str(e)}')


@data_bp.route('/search_reports', methods=['POST'])
def search_reports():
    """搜索年报"""
    try:
        data = request.json
        keyword = data.get('keyword')
        stock_codes = data.get('stock_codes', [])
        year = data.get('year')
        
        reports = analysis_service.db.search_reports(keyword, stock_codes, year)
        return success_response(reports)
    except Exception as e:
        return error_response(f'搜索失败: {str(e)}')


@data_bp.route('/clean_duplicates', methods=['POST'])
def clean_duplicates():
    """清理重复数据"""
    try:
        # 清理重复的年报记录
        reports_cleaned = analysis_service.db.clean_duplicate_reports()
        
        # 清理重复的关键词分析记录
        analysis_cleaned = analysis_service.db.clean_duplicate_keyword_analysis()
        
        return success_response({
            'message': f'清理完成：删除了 {reports_cleaned} 条重复年报记录，{analysis_cleaned} 条重复分析记录',
            'reports_cleaned': reports_cleaned,
            'analysis_cleaned': analysis_cleaned
        })
    except Exception as e:
        return error_response(f'清理失败: {str(e)}')


@data_bp.route('/debug_database', methods=['GET'])
def debug_database():
    """调试数据库状态"""
    try:
        # 获取所有年报
        all_reports = analysis_service.db.get_all_reports()
        
        # 获取所有公司
        all_companies = analysis_service.db.get_all_companies()
        
        # 统计信息
        stock_codes = set(report['stock_code'] for report in all_reports)
        
        debug_info = {
            'total_reports': len(all_reports),
            'total_companies': len(all_companies),
            'unique_stock_codes': len(stock_codes),
            'stock_codes_list': sorted(stock_codes),
            'sample_reports': all_reports[:5] if all_reports else [],
            'sample_companies': all_companies[:5] if all_companies else []
        }
        
        return success_response(debug_info)
    except Exception as e:
        return error_response(f'调试失败: {str(e)}')
