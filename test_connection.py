#!/usr/bin/env python3
"""
前后端连接测试脚本
"""
import requests
import json
import time

def test_backend_connection():
    """测试后端连接"""
    base_url = "http://localhost:5000/api"
    
    print("🔍 测试后端连接...")
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查成功: {data.get('data', {}).get('message', '服务正常')}")
            print(f"   响应时间: {response.elapsed.total_seconds()*1000:.0f}ms")
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器 (http://localhost:5000)")
        print("   请确保后端服务器已启动: python backend/app.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
        return False
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False
    
    # 测试获取公司列表
    try:
        response = requests.get(f"{base_url}/companies", timeout=5)
        if response.status_code == 200:
            data = response.json()
            companies = data.get('data', [])
            print(f"✅ 获取公司列表成功: 共 {len(companies)} 家公司")
        else:
            print(f"⚠️ 获取公司列表失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"⚠️ 获取公司列表错误: {e}")
    
    return True

def test_frontend_connection():
    """测试前端连接"""
    frontend_url = "http://localhost:3001"
    
    print("\n🔍 测试前端连接...")
    
    try:
        response = requests.get(frontend_url, timeout=5)
        if response.status_code == 200:
            print(f"✅ 前端服务正常: {frontend_url}")
            print(f"   响应时间: {response.elapsed.total_seconds()*1000:.0f}ms")
        else:
            print(f"❌ 前端服务异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到前端服务器 ({frontend_url})")
        print("   请确保前端服务器已启动: cd frontend && npm run dev")
        return False
    except requests.exceptions.Timeout:
        print("❌ 前端连接超时")
        return False
    except Exception as e:
        print(f"❌ 前端连接错误: {e}")
        return False
    
    return True

def test_api_endpoints():
    """测试主要API端点"""
    base_url = "http://localhost:5000/api"
    
    print("\n🔍 测试API端点...")
    
    endpoints = [
        ("/health", "GET", "健康检查"),
        ("/companies", "GET", "获取公司列表"),
    ]
    
    for endpoint, method, description in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            else:
                response = requests.post(f"{base_url}{endpoint}", timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {description}: 正常")
            else:
                print(f"⚠️ {description}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {description}: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 前后端连接测试")
    print("=" * 50)
    
    # 测试后端
    backend_ok = test_backend_connection()
    
    # 测试前端
    frontend_ok = test_frontend_connection()
    
    # 测试API端点
    if backend_ok:
        test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    print(f"后端服务: {'✅ 正常' if backend_ok else '❌ 异常'}")
    print(f"前端服务: {'✅ 正常' if frontend_ok else '❌ 异常'}")
    
    if backend_ok and frontend_ok:
        print("\n🎉 前后端连接测试通过！")
        print("📱 前端地址: http://localhost:3001")
        print("🔧 后端地址: http://localhost:5000")
        print("📚 API文档: http://localhost:5000/api/health")
    else:
        print("\n⚠️ 部分服务异常，请检查服务状态")
        if not backend_ok:
            print("   - 启动后端: cd backend && python app.py")
        if not frontend_ok:
            print("   - 启动前端: cd frontend && npm run dev")

if __name__ == "__main__":
    main()
