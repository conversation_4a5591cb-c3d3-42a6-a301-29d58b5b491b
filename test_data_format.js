// 测试数据格式转换
const mockAnalysisData = {
  success: true,
  analysis_id: "keyword_analysis_1234567890",
  data: {
    "000001": {
      "平安银行2024年年度报告.txt": {
        "人工智能": 15,
        "大数据": 8,
        "云计算": 12
      },
      "平安银行2024年第三季度报告.txt": {
        "人工智能": 3,
        "大数据": 2,
        "云计算": 5
      }
    },
    "000002": {
      "万科A2024年年度报告.txt": {
        "人工智能": 2,
        "大数据": 1,
        "云计算": 3
      }
    }
  },
  related_party_analysis: {},
  message: "分析完成，共分析 3 个年报文件"
}

// 模拟前端数据转换逻辑
function transformAnalysisData(analysisData) {
  const flattenedResults = []
  
  for (const [stockCode, files] of Object.entries(analysisData.data || {})) {
    for (const [fileName, keywords] of Object.entries(files)) {
      for (const [keyword, count] of Object.entries(keywords)) {
        flattenedResults.push({
          stock_code: stockCode,
          company_name: stockCode, // 临时使用股票代码
          file_name: fileName,
          keyword: keyword,
          count: count,
          analysis_id: analysisData.analysis_id
        })
      }
    }
  }
  
  return flattenedResults
}

// 测试转换
const result = transformAnalysisData(mockAnalysisData)
console.log('转换结果:')
console.log(JSON.stringify(result, null, 2))

console.log('\n预期的表格显示:')
result.forEach((item, index) => {
  console.log(`${index + 1}. ${item.company_name} | ${item.keyword} | ${item.count} | ${item.file_name}`)
})
