@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply btn bg-black text-white hover:bg-gray-800 active:bg-gray-900 shadow-sm;
  }

  .btn-secondary {
    @apply btn bg-white text-gray-900 border border-gray-200 hover:bg-gray-50 active:bg-gray-100 shadow-sm;
  }

  .btn-ghost {
    @apply btn text-gray-600 hover:text-gray-900 hover:bg-gray-100 active:bg-gray-200;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 active:bg-red-800 shadow-sm;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-6 text-base;
  }

  /* 输入组件 */
  .input {
    @apply flex h-10 w-full rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  .textarea {
    @apply input min-h-[80px] resize-none;
  }

  .select {
    @apply input cursor-pointer;
  }

  /* 卡片组件 */
  .card {
    @apply rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200;
  }

  .card-hover {
    @apply card hover:shadow-md hover:border-gray-300;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6 pb-4;
  }

  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight text-gray-900;
  }

  .card-description {
    @apply text-sm text-gray-600;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* 导航组件 */
  .nav-item {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium text-gray-600 transition-all duration-200 hover:text-gray-900 hover:bg-gray-100 active:bg-gray-200;
  }

  .nav-item-active {
    @apply nav-item bg-gray-100 text-gray-900;
  }

  /* 表格组件 */
  .table {
    @apply w-full caption-bottom text-sm;
  }

  .table-header {
    @apply border-b border-gray-200;
  }

  .table-header-cell {
    @apply h-12 px-4 text-left align-middle font-medium text-gray-600;
  }

  .table-body {
    @apply [&_tr:last-child]:border-0;
  }

  .table-row {
    @apply border-b border-gray-100 transition-colors hover:bg-gray-50;
  }

  .table-cell {
    @apply p-4 align-middle text-gray-900;
  }

  /* 徽章组件 */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors;
  }

  .badge-default {
    @apply badge bg-gray-100 text-gray-800;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply badge bg-red-100 text-red-800;
  }

  /* 分隔符 */
  .separator {
    @apply shrink-0 bg-gray-200;
  }

  .separator-horizontal {
    @apply separator h-[1px] w-full;
  }

  .separator-vertical {
    @apply separator h-full w-[1px];
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 动画工具类 */
  .animate-in {
    animation-duration: 0.2s;
    animation-fill-mode: both;
  }

  .animate-out {
    animation-duration: 0.15s;
    animation-fill-mode: both;
  }

  .fade-in {
    animation-name: fadeIn;
  }

  .fade-out {
    animation-name: fadeOut;
  }

  .slide-in-from-top {
    animation-name: slideInFromTop;
  }

  .slide-in-from-bottom {
    animation-name: slideInFromBottom;
  }

  .slide-in-from-left {
    animation-name: slideInFromLeft;
  }

  .slide-in-from-right {
    animation-name: slideInFromRight;
  }

  .zoom-in {
    animation-name: zoomIn;
  }

  .zoom-out {
    animation-name: zoomOut;
  }
}

/* 动画关键帧 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInFromTop {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInFromBottom {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInFromLeft {
  from { transform: translateX(-10px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
  from { transform: translateX(10px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes zoomIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes zoomOut {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0.95); opacity: 0; }
}
