'use client'

import { useState } from 'react'
import { AnalysisForm } from '@/components/AnalysisForm'
import { ResultsDisplay } from '@/components/ResultsDisplay'
import { TaskProgress } from '@/components/TaskProgress'
import { Header } from '@/components/Header'
import { Sidebar } from '@/components/Sidebar'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Bot, Upload, FileText, Database, TrendingUp, Users } from 'lucide-react'

export default function Home() {
  const [activeTab, setActiveTab] = useState('analysis')
  const [currentTask, setCurrentTask] = useState<string | null>(null)
  const [analysisResults, setAnalysisResults] = useState<any>(null)

  const handleTaskStart = (taskId: string) => {
    setCurrentTask(taskId)
    setAnalysisResults(null)
  }

  const handleTaskComplete = (results: any) => {
    setCurrentTask(null)
    setAnalysisResults(results)
  }

  const getPageTitle = () => {
    switch (activeTab) {
      case 'analysis':
        return '在线年报分析'
      case 'keyword':
        return '本地关键词分析'
      case 'ai':
        return 'AI 智能分析'
      case 'import':
        return '数据导入管理'
      default:
        return '年报分析工具'
    }
  }

  const getPageSubtitle = () => {
    switch (activeTab) {
      case 'analysis':
        return '从巨潮资讯网爬取并分析年报数据'
      case 'keyword':
        return '分析本地数据库中的年报文件'
      case 'ai':
        return '使用AI技术进行深度分析和洞察'
      case 'import':
        return '管理和导入本地TXT文件到数据库'
      default:
        return '智能年报分析与关键词统计'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* 侧边栏 */}
      <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col min-w-0">
        <Header title={getPageTitle()} subtitle={getPageSubtitle()} />

        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto">
            {/* 任务进度 */}
            {currentTask && (
              <div className="mb-6">
                <TaskProgress
                  taskId={currentTask}
                  onComplete={handleTaskComplete}
                />
              </div>
            )}

            {/* 内容区域 */}
            {activeTab === 'analysis' && (
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <AnalysisForm
                    mode="online"
                    onTaskStart={handleTaskStart}
                  />
                </div>
                <div className="space-y-6">
                  {analysisResults ? (
                    <ResultsDisplay results={analysisResults} />
                  ) : (
                    <Card>
                      <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                        <TrendingUp className="w-12 h-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          等待分析结果
                        </h3>
                        <p className="text-gray-500 max-w-sm">
                          请在左侧配置分析参数并开始分析，结果将在这里显示
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'keyword' && (
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <AnalysisForm
                    mode="local"
                    onTaskStart={handleTaskStart}
                  />
                </div>
                <div className="space-y-6">
                  {analysisResults ? (
                    <ResultsDisplay results={analysisResults} />
                  ) : (
                    <Card>
                      <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                        <Database className="w-12 h-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          等待分析结果
                        </h3>
                        <p className="text-gray-500 max-w-sm">
                          请在左侧配置关键词分析参数，结果将在这里显示
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'ai' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                        <Bot className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <CardTitle>AI 智能分析</CardTitle>
                        <p className="text-sm text-gray-500 mt-1">
                          使用AI技术对年报内容进行深度分析
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h3 className="font-medium text-gray-900">功能特性</h3>
                        <ul className="space-y-2 text-sm text-gray-600">
                          <li className="flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                            <span>智能摘要生成</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                            <span>关键信息提取</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                            <span>趋势分析预测</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                            <span>风险评估报告</span>
                          </li>
                        </ul>
                      </div>
                      <div className="flex items-center justify-center">
                        <Button size="lg" className="w-full">
                          <Bot className="w-4 h-4 mr-2" />
                          开始AI分析
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'import' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg">
                        <Upload className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <CardTitle>数据导入管理</CardTitle>
                        <p className="text-sm text-gray-500 mt-1">
                          导入本地TXT文件到数据库
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center">
                        <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-3">
                          <FileText className="w-6 h-6 text-blue-600" />
                        </div>
                        <h3 className="font-medium text-gray-900 mb-2">批量导入</h3>
                        <p className="text-sm text-gray-500">
                          支持批量导入多个TXT文件
                        </p>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-3">
                          <Database className="w-6 h-6 text-yellow-600" />
                        </div>
                        <h3 className="font-medium text-gray-900 mb-2">重复检测</h3>
                        <p className="text-sm text-gray-500">
                          自动检测并跳过重复文件
                        </p>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-3">
                          <Users className="w-6 h-6 text-green-600" />
                        </div>
                        <h3 className="font-medium text-gray-900 mb-2">数据管理</h3>
                        <p className="text-sm text-gray-500">
                          统一管理所有导入的数据
                        </p>
                      </div>
                    </div>
                    <div className="mt-6 flex justify-center">
                      <Button size="lg">
                        <Upload className="w-4 h-4 mr-2" />
                        选择文件导入
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}
