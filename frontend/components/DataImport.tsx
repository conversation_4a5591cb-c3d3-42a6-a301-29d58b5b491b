'use client'

import { useState, useRef } from 'react'
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Folder,
  Database,
  Trash2,
  RefreshCw
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { Loading } from './ui/Loading'

interface FileItem {
  id: string
  name: string
  size: number
  status: 'pending' | 'uploading' | 'success' | 'error' | 'duplicate'
  progress?: number
  error?: string
}

export function DataImport() {
  const [files, setFiles] = useState<FileItem[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [stats, setStats] = useState({
    total: 0,
    success: 0,
    error: 0,
    duplicate: 0
  })
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || [])
    const newFiles: FileItem[] = selectedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      status: 'pending'
    }))
    
    setFiles(prev => [...prev, ...newFiles])
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    const droppedFiles = Array.from(event.dataTransfer.files)
    const newFiles: FileItem[] = droppedFiles
      .filter(file => file.name.endsWith('.txt'))
      .map(file => ({
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        status: 'pending'
      }))
    
    setFiles(prev => [...prev, ...newFiles])
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
  }

  const startUpload = async () => {
    setIsUploading(true)
    const pendingFiles = files.filter(f => f.status === 'pending')
    
    for (const file of pendingFiles) {
      // 更新状态为上传中
      setFiles(prev => prev.map(f => 
        f.id === file.id ? { ...f, status: 'uploading', progress: 0 } : f
      ))

      // 模拟上传进度
      for (let progress = 0; progress <= 100; progress += 20) {
        await new Promise(resolve => setTimeout(resolve, 200))
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, progress } : f
        ))
      }

      // 模拟上传结果
      const random = Math.random()
      let status: FileItem['status']
      let error: string | undefined

      if (random < 0.1) {
        status = 'duplicate'
      } else if (random < 0.2) {
        status = 'error'
        error = '文件格式不正确或内容无法解析'
      } else {
        status = 'success'
      }

      setFiles(prev => prev.map(f => 
        f.id === file.id ? { ...f, status, error, progress: 100 } : f
      ))
    }

    // 更新统计
    const newStats = files.reduce((acc, file) => {
      acc.total++
      if (file.status === 'success') acc.success++
      else if (file.status === 'error') acc.error++
      else if (file.status === 'duplicate') acc.duplicate++
      return acc
    }, { total: 0, success: 0, error: 0, duplicate: 0 })

    setStats(newStats)
    setIsUploading(false)
  }

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id))
  }

  const clearAll = () => {
    setFiles([])
    setStats({ total: 0, success: 0, error: 0, duplicate: 0 })
  }

  const getStatusIcon = (status: FileItem['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />
      case 'duplicate':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />
      case 'uploading':
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
      default:
        return <FileText className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: FileItem['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="success" size="sm">成功</Badge>
      case 'error':
        return <Badge variant="error" size="sm">失败</Badge>
      case 'duplicate':
        return <Badge variant="warning" size="sm">重复</Badge>
      case 'uploading':
        return <Badge variant="info" size="sm">上传中</Badge>
      default:
        return <Badge variant="default" size="sm">等待</Badge>
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Database className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                <p className="text-sm text-gray-500">总文件数</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{stats.success}</p>
                <p className="text-sm text-gray-500">导入成功</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <XCircle className="w-5 h-5 text-red-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{stats.error}</p>
                <p className="text-sm text-gray-500">导入失败</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{stats.duplicate}</p>
                <p className="text-sm text-gray-500">重复文件</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 文件上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="w-5 h-5" />
            <span>文件导入</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <Folder className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              拖拽文件到此处或点击选择
            </h3>
            <p className="text-gray-500 mb-4">
              支持 .txt 格式的年报文件，支持批量上传
            </p>
            <Button variant="secondary">
              <Upload className="w-4 h-4 mr-2" />
              选择文件
            </Button>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".txt"
            onChange={handleFileSelect}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* 文件列表 */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>文件列表 ({files.length})</CardTitle>
              <div className="flex items-center space-x-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={clearAll}
                  disabled={isUploading}
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  清空
                </Button>
                <Button
                  onClick={startUpload}
                  disabled={isUploading || files.filter(f => f.status === 'pending').length === 0}
                  loading={isUploading}
                >
                  <Upload className="w-4 h-4 mr-1" />
                  开始导入
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {files.map((file) => (
                <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3 flex-1">
                    {getStatusIcon(file.status)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                      {file.error && (
                        <p className="text-xs text-red-600 mt-1">{file.error}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    {file.status === 'uploading' && file.progress !== undefined && (
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${file.progress}%` }}
                        />
                      </div>
                    )}
                    
                    {getStatusBadge(file.status)}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                      disabled={isUploading}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
