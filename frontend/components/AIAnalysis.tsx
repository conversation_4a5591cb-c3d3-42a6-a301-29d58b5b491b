'use client'

import { useState } from 'react'
import { 
  Bot, 
  Send, 
  Sparkles, 
  FileText, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  Clock,
  MessageSquare
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { Button } from './ui/Button'
import { Textarea } from './ui/Input'
import { Badge } from './ui/Badge'
import { Loading } from './ui/Loading'

interface AIAnalysisProps {
  onAnalysisStart?: (content: string) => void
}

export function AIAnalysis({ onAnalysisStart }: AIAnalysisProps) {
  const [inputContent, setInputContent] = useState('')
  const [analysisResults, setAnalysisResults] = useState<any>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisHistory, setAnalysisHistory] = useState<any[]>([])

  const handleAnalysis = async () => {
    if (!inputContent.trim()) return
    
    setIsAnalyzing(true)
    onAnalysisStart?.(inputContent)
    
    // 模拟AI分析
    setTimeout(() => {
      const mockResult = {
        id: Date.now(),
        input: inputContent,
        timestamp: new Date(),
        summary: '基于您提供的内容，我发现了以下关键信息...',
        keyPoints: [
          '技术创新投入持续增长',
          '市场份额稳步提升',
          '研发团队规模扩大',
          '产品线多元化发展'
        ],
        sentiment: 'positive',
        confidence: 0.85,
        recommendations: [
          '建议关注技术发展趋势',
          '加强市场竞争分析',
          '优化资源配置策略'
        ]
      }
      
      setAnalysisResults(mockResult)
      setAnalysisHistory(prev => [mockResult, ...prev])
      setIsAnalyzing(false)
    }, 3000)
  }

  const features = [
    {
      icon: Sparkles,
      title: '智能摘要',
      description: '自动提取年报核心内容，生成简洁摘要'
    },
    {
      icon: TrendingUp,
      title: '趋势分析',
      description: '识别业务发展趋势和关键变化'
    },
    {
      icon: AlertCircle,
      title: '风险识别',
      description: '发现潜在风险点和关注事项'
    },
    {
      icon: CheckCircle,
      title: '机会洞察',
      description: '挖掘投资机会和发展潜力'
    }
  ]

  return (
    <div className="space-y-6">
      {/* 功能介绍 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {features.map((feature, index) => {
          const Icon = feature.icon
          return (
            <Card key={index} className="text-center">
              <CardContent className="pt-6">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
                  <Icon className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-sm text-gray-500">{feature.description}</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* 分析界面 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="w-5 h-5" />
              <span>输入分析内容</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="请输入需要分析的年报内容或提出您的问题..."
              value={inputContent}
              onChange={(e) => setInputContent(e.target.value)}
              className="min-h-[200px]"
            />
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {inputContent.length} / 5000 字符
              </div>
              <Button
                onClick={handleAnalysis}
                disabled={!inputContent.trim() || isAnalyzing}
                loading={isAnalyzing}
              >
                <Send className="w-4 h-4 mr-2" />
                开始分析
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 分析结果 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bot className="w-5 h-5" />
              <span>AI 分析结果</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isAnalyzing ? (
              <div className="flex flex-col items-center justify-center py-12">
                <Loading size="lg" text="AI正在分析中..." />
                <p className="text-sm text-gray-500 mt-4">
                  请稍候，这可能需要几秒钟时间
                </p>
              </div>
            ) : analysisResults ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Badge variant="success">分析完成</Badge>
                  <div className="text-sm text-gray-500">
                    置信度: {(analysisResults.confidence * 100).toFixed(0)}%
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">智能摘要</h4>
                  <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                    {analysisResults.summary}
                  </p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">关键要点</h4>
                  <ul className="space-y-1">
                    {analysisResults.keyPoints.map((point: string, index: number) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span>{point}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">建议</h4>
                  <ul className="space-y-1">
                    {analysisResults.recommendations.map((rec: string, index: number) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Bot className="w-12 h-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  等待分析内容
                </h3>
                <p className="text-gray-500 max-w-sm">
                  请在左侧输入需要分析的年报内容，AI将为您提供深度分析和洞察
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 分析历史 */}
      {analysisHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5" />
              <span>分析历史</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analysisHistory.slice(0, 3).map((item) => (
                <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="default" size="sm">
                      {item.timestamp.toLocaleString()}
                    </Badge>
                    <div className="text-sm text-gray-500">
                      置信度: {(item.confidence * 100).toFixed(0)}%
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {item.summary}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
