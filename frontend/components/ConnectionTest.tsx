'use client'

import { useState } from 'react'
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Server
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { apiMethods } from '@/lib/api'

interface ConnectionStatus {
  status: 'checking' | 'connected' | 'disconnected' | 'error'
  message: string
  latency?: number
  timestamp?: Date
}

export function ConnectionTest() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: 'disconnected',
    message: '未检测'
  })
  const [isChecking, setIsChecking] = useState(false)

  const checkConnection = async () => {
    setIsChecking(true)
    setConnectionStatus({
      status: 'checking',
      message: '正在检测连接...'
    })

    const startTime = Date.now()

    try {
      // 尝试健康检查来测试连接
      const response = await apiMethods.healthCheck()
      const latency = Date.now() - startTime

      if (response.status === 200) {
        const data = response.data
        setConnectionStatus({
          status: 'connected',
          message: data.data?.message || '后端连接正常',
          latency,
          timestamp: new Date()
        })
      } else {
        setConnectionStatus({
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`,
          timestamp: new Date()
        })
      }
    } catch (error: any) {
      const latency = Date.now() - startTime
      let message = '连接失败'
      
      if (error.code === 'ECONNREFUSED') {
        message = '后端服务器未启动'
      } else if (error.code === 'ECONNABORTED') {
        message = '连接超时'
      } else if (error.response?.status === 404) {
        message = 'API接口不存在'
      } else if (error.response?.status === 500) {
        message = '服务器内部错误'
      } else if (error.message) {
        message = error.message
      }

      setConnectionStatus({
        status: 'disconnected',
        message,
        latency,
        timestamp: new Date()
      })
    } finally {
      setIsChecking(false)
    }
  }

  const getStatusIcon = () => {
    switch (connectionStatus.status) {
      case 'checking':
        return <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
      case 'connected':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'disconnected':
        return <WifiOff className="w-5 h-5 text-red-600" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />
      default:
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />
    }
  }

  const getStatusBadge = () => {
    switch (connectionStatus.status) {
      case 'checking':
        return <Badge variant="info">检测中</Badge>
      case 'connected':
        return <Badge variant="success">已连接</Badge>
      case 'disconnected':
        return <Badge variant="error">未连接</Badge>
      case 'error':
        return <Badge variant="error">错误</Badge>
      default:
        return <Badge variant="warning">未知</Badge>
    }
  }

  const getStatusColor = () => {
    switch (connectionStatus.status) {
      case 'connected':
        return 'text-green-600'
      case 'disconnected':
      case 'error':
        return 'text-red-600'
      case 'checking':
        return 'text-blue-600'
      default:
        return 'text-yellow-600'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Server className="w-5 h-5" />
          <span>后端连接状态</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 状态显示 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getStatusIcon()}
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900">连接状态</span>
                  {getStatusBadge()}
                </div>
                <p className={`text-sm ${getStatusColor()}`}>
                  {connectionStatus.message}
                </p>
              </div>
            </div>
            
            <Button
              onClick={checkConnection}
              disabled={isChecking}
              loading={isChecking}
              size="sm"
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              检测
            </Button>
          </div>

          {/* 详细信息 */}
          {connectionStatus.latency !== undefined && (
            <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div>
                <p className="text-sm font-medium text-gray-700">响应时间</p>
                <p className="text-lg font-semibold text-gray-900">
                  {connectionStatus.latency}ms
                </p>
              </div>
              {connectionStatus.timestamp && (
                <div>
                  <p className="text-sm font-medium text-gray-700">检测时间</p>
                  <p className="text-sm text-gray-600">
                    {connectionStatus.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* 连接信息 */}
          <div className="bg-gray-50 rounded-lg p-3">
            <h4 className="text-sm font-medium text-gray-700 mb-2">连接信息</h4>
            <div className="space-y-1 text-xs text-gray-600">
              <div>后端地址: http://localhost:5000</div>
              <div>API前缀: /api</div>
              <div>超时时间: 30秒</div>
            </div>
          </div>

          {/* 故障排除 */}
          {connectionStatus.status === 'disconnected' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <h4 className="text-sm font-medium text-red-800 mb-2">故障排除</h4>
              <ul className="space-y-1 text-xs text-red-700">
                <li>• 确保后端服务器已启动 (python app.py)</li>
                <li>• 检查端口5000是否被占用</li>
                <li>• 确认防火墙设置允许连接</li>
                <li>• 检查网络连接是否正常</li>
              </ul>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
