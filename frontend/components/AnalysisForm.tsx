'use client'

import { useState } from 'react'
import { Play, Settings, Calendar, Search, ChevronDown, ChevronUp } from 'lucide-react'
import toast from 'react-hot-toast'
import { apiMethods } from '@/lib/api'
import { Button } from './ui/Button'
import { Input, Textarea } from './ui/Input'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { cn } from '@/lib/utils'

interface AnalysisFormProps {
  mode: 'online' | 'local'
  onTaskStart: (taskId: string) => void
  onAnalysisComplete?: (results: any) => void
}

export function AnalysisForm({ mode, onTaskStart, onAnalysisComplete }: AnalysisFormProps) {
  const [formData, setFormData] = useState({
    stockCodes: '',
    keywords: '',
    searchKeyword: '年度报告',
    startDate: '2024-01-01',
    endDate: '2025-12-31',
    relatedParties: '',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.stockCodes.trim()) {
      toast.error('请输入股票代码')
      return
    }
    
    if (!formData.keywords.trim()) {
      toast.error('请输入关键词')
      return
    }

    setIsLoading(true)

    try {
      const payload = {
        stock_codes: formData.stockCodes,
        keywords: formData.keywords,
        search_keyword: formData.searchKeyword,
        start_date: formData.startDate,
        end_date: formData.endDate,
        use_online: mode === 'online',
        related_parties: formData.relatedParties,
      }

      console.log('🔍 发送分析请求:', {
        mode,
        payload,
        endpoint: mode === 'online' ? 'startAnalysis' : 'keywordAnalysis'
      })

      const response = mode === 'online'
        ? await apiMethods.startAnalysis(payload)
        : await apiMethods.keywordAnalysis(payload)

      console.log('📥 收到响应:', response)
      
      if (response.data.success) {
        if (mode === 'online') {
          onTaskStart(response.data.data.task_id || response.data.task_id)
          toast.success('分析任务已启动')
        } else {
          // 处理本地分析结果
          const analysisData = response.data.data
          console.log('📊 本地分析结果:', analysisData)

          if (onAnalysisComplete && analysisData) {
            // 转换数据格式为ResultsDisplay期望的格式
            const flattenedResults = []

            // 获取所有股票代码以便批量获取公司名称
            const stockCodes = Object.keys(analysisData || {})

            // 先转换数据结构
            for (const [stockCode, files] of Object.entries(analysisData || {})) {
              for (const [fileName, keywords] of Object.entries(files as any)) {
                for (const [keyword, count] of Object.entries(keywords as any)) {
                  flattenedResults.push({
                    stock_code: stockCode,
                    company_name: stockCode, // 临时使用股票代码，后续会更新
                    file_name: fileName,
                    keyword: keyword,
                    count: count
                  })
                }
              }
            }

            console.log('📊 转换后的结果:', flattenedResults)

            // 异步获取公司名称并更新结果
            Promise.all(
              stockCodes.map(code =>
                apiMethods.getCompanyByCode(code)
                  .then(response => ({ code, name: response.data.data?.company_name || code }))
                  .catch(() => ({ code, name: code }))
              )
            ).then(companyNames => {
              const nameMap = Object.fromEntries(companyNames.map(c => [c.code, c.name]))

              // 更新公司名称
              const updatedResults = flattenedResults.map(item => ({
                ...item,
                company_name: nameMap[item.stock_code] || item.stock_code,
                analysis_id: analysisData.analysis_id // 添加analysis_id用于查看上下文
              }))

              console.log('📊 更新公司名称后的结果:', updatedResults)
              onAnalysisComplete(updatedResults)
            }).catch(error => {
              console.error('获取公司名称失败:', error)
              // 即使获取公司名称失败，也要显示结果
              onAnalysisComplete(flattenedResults)
            })
          }
          toast.success('关键词分析完成')
        }
      } else {
        toast.error(response.data.message || '分析失败')
      }
    } catch (error: any) {
      console.error('❌ 请求失败:', error)
      console.error('   错误详情:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      })
      toast.error(error.response?.data?.message || '请求失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="animate-in fade-in slide-in-from-bottom">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-lg">
              {mode === 'online' ? (
                <Search className="w-5 h-5 text-gray-700" />
              ) : (
                <Settings className="w-5 h-5 text-gray-700" />
              )}
            </div>
            <div>
              <CardTitle>
                {mode === 'online' ? '在线年报分析' : '本地关键词分析'}
              </CardTitle>
              <p className="text-sm text-gray-500 mt-1">
                {mode === 'online'
                  ? '从巨潮资讯网爬取并分析年报数据'
                  : '分析本地数据库中的年报文件'
                }
              </p>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基础配置 */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Textarea
                label="股票代码"
                placeholder="每行一个股票代码，例如：&#10;000001&#10;000002&#10;300454"
                value={formData.stockCodes}
                onChange={(e) => setFormData({ ...formData, stockCodes: e.target.value })}
                className="h-32"
                required
                helperText="支持多个股票代码，每行一个"
              />

              <Textarea
                label="分析关键词"
                placeholder="每行一个关键词，例如：&#10;人工智能&#10;大数据&#10;云计算"
                value={formData.keywords}
                onChange={(e) => setFormData({ ...formData, keywords: e.target.value })}
                className="h-32"
                required
                helperText="支持多个关键词，每行一个"
              />
            </div>
          </div>

          {/* 高级配置 */}
          <div className="space-y-4">
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center space-x-2 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors"
            >
              {showAdvanced ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
              <span>高级设置</span>
            </button>

            {showAdvanced && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg animate-in fade-in slide-in-from-top">
                {mode === 'online' && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input
                      label="搜索关键词"
                      value={formData.searchKeyword}
                      onChange={(e) => setFormData({ ...formData, searchKeyword: e.target.value })}
                      helperText="用于筛选年报类型"
                    />

                    <Input
                      label="开始日期"
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    />

                    <Input
                      label="结束日期"
                      type="date"
                      value={formData.endDate}
                      onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                    />
                  </div>
                )}

                <Textarea
                  label="关联方分析（可选）"
                  placeholder="每行一个关联方名称，例如：&#10;清华大学&#10;中科院&#10;华为技术"
                  value={formData.relatedParties}
                  onChange={(e) => setFormData({ ...formData, relatedParties: e.target.value })}
                  className="h-24"
                  helperText="分析年报中与这些关联方的合作创新情况"
                />
              </div>
            )}
          </div>

          {/* 提交按钮 */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              {mode === 'online'
                ? '将从巨潮资讯网下载并分析年报数据'
                : '将分析本地数据库中已有的年报文件'
              }
            </div>

            <Button
              type="submit"
              loading={isLoading}
              size="lg"
              className="min-w-[120px]"
            >
              <Play className="w-4 h-4" />
              <span>{isLoading ? '分析中...' : '开始分析'}</span>
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
