'use client'

import { useState } from 'react'
import { Play, Settings, Calendar, Search, ChevronDown, ChevronUp } from 'lucide-react'
import toast from 'react-hot-toast'
import { api } from '@/lib/api'
import { <PERSON><PERSON> } from './ui/Button'
import { Input, Textarea } from './ui/Input'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { cn } from '@/lib/utils'

interface AnalysisFormProps {
  mode: 'online' | 'local'
  onTaskStart: (taskId: string) => void
}

export function AnalysisForm({ mode, onTaskStart }: AnalysisFormProps) {
  const [formData, setFormData] = useState({
    stockCodes: '',
    keywords: '',
    searchKeyword: '年度报告',
    startDate: '2024-01-01',
    endDate: '2025-12-31',
    relatedParties: '',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.stockCodes.trim()) {
      toast.error('请输入股票代码')
      return
    }
    
    if (!formData.keywords.trim()) {
      toast.error('请输入关键词')
      return
    }

    setIsLoading(true)
    
    try {
      const endpoint = mode === 'online' ? '/api/start_analysis' : '/api/keyword_analysis'
      const payload = {
        stock_codes: formData.stockCodes,
        keywords: formData.keywords,
        search_keyword: formData.searchKeyword,
        start_date: formData.startDate,
        end_date: formData.endDate,
        use_online: mode === 'online',
        related_parties: formData.relatedParties,
      }

      const response = await api.post(endpoint, payload)
      
      if (response.data.success) {
        if (mode === 'online') {
          onTaskStart(response.data.task_id)
          toast.success('分析任务已启动')
        } else {
          toast.success('关键词分析完成')
          // 处理本地分析结果
        }
      } else {
        toast.error(response.data.message || '分析失败')
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || '请求失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="card">
      <div className="flex items-center space-x-3 mb-6">
        <div className="flex items-center justify-center w-8 h-8 bg-primary-100 rounded-lg">
          {mode === 'online' ? (
            <Search className="w-5 h-5 text-primary-600" />
          ) : (
            <Settings className="w-5 h-5 text-primary-600" />
          )}
        </div>
        <h2 className="text-xl font-semibold">
          {mode === 'online' ? '在线年报分析' : '本地关键词分析'}
        </h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              股票代码 *
            </label>
            <textarea
              className="textarea-field h-24"
              placeholder="每行一个股票代码&#10;例如：&#10;000001&#10;000002"
              value={formData.stockCodes}
              onChange={(e) => setFormData({ ...formData, stockCodes: e.target.value })}
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              关键词 *
            </label>
            <textarea
              className="textarea-field h-24"
              placeholder="每行一个关键词&#10;例如：&#10;人工智能&#10;大数据&#10;云计算"
              value={formData.keywords}
              onChange={(e) => setFormData({ ...formData, keywords: e.target.value })}
              required
            />
          </div>
        </div>

        {mode === 'online' && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索关键词
              </label>
              <input
                type="text"
                className="input-field"
                value={formData.searchKeyword}
                onChange={(e) => setFormData({ ...formData, searchKeyword: e.target.value })}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                开始日期
              </label>
              <input
                type="date"
                className="input-field"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                结束日期
              </label>
              <input
                type="date"
                className="input-field"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
              />
            </div>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            关联方（可选）
          </label>
          <textarea
            className="textarea-field h-20"
            placeholder="每行一个关联方名称&#10;例如：&#10;清华大学&#10;中科院"
            value={formData.relatedParties}
            onChange={(e) => setFormData({ ...formData, relatedParties: e.target.value })}
          />
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Play className="w-4 h-4" />
            <span>{isLoading ? '分析中...' : '开始分析'}</span>
          </button>
        </div>
      </form>
    </div>
  )
}
