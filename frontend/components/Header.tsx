'use client'

import { useState } from 'react'
import {
  Database,
  Download,
  Bell,
  User,
  Search,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { Button } from './ui/Button'
import { cn } from '@/lib/utils'

interface HeaderProps {
  title?: string
  subtitle?: string
}

export function Header({ title, subtitle }: HeaderProps) {
  const [dbStatus, setDbStatus] = useState<'connected' | 'disconnected' | 'loading'>('connected')
  const [notifications, setNotifications] = useState(3)

  const handleRefreshDb = () => {
    setDbStatus('loading')
    setTimeout(() => {
      setDbStatus('connected')
    }, 2000)
  }

  const handleExport = () => {
    // 导出逻辑
    console.log('导出数据')
  }

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-40 backdrop-blur-sm bg-white/95">
      <div className="flex items-center justify-between px-6 py-4">
        {/* 标题区域 */}
        <div className="flex-1">
          {title && (
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                {title}
              </h1>
              {subtitle && (
                <p className="text-sm text-gray-500 mt-0.5">
                  {subtitle}
                </p>
              )}
            </div>
          )}
        </div>

        {/* 搜索框 */}
        <div className="flex-1 max-w-md mx-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索公司或关键词..."
              className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            />
          </div>
        </div>

        {/* 右侧操作区 */}
        <div className="flex items-center space-x-4">
          {/* 数据库状态 */}
          <div className="flex items-center space-x-2">
            <div className={cn(
              'flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm font-medium',
              dbStatus === 'connected' && 'bg-green-50 text-green-700',
              dbStatus === 'disconnected' && 'bg-red-50 text-red-700',
              dbStatus === 'loading' && 'bg-yellow-50 text-yellow-700'
            )}>
              {dbStatus === 'connected' && <CheckCircle className="w-4 h-4" />}
              {dbStatus === 'disconnected' && <AlertCircle className="w-4 h-4" />}
              {dbStatus === 'loading' && <RefreshCw className="w-4 h-4 animate-spin" />}
              <span>
                {dbStatus === 'connected' && '数据库已连接'}
                {dbStatus === 'disconnected' && '数据库断开'}
                {dbStatus === 'loading' && '连接中...'}
              </span>
            </div>
            <button
              onClick={handleRefreshDb}
              className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
              title="刷新连接"
            >
              <RefreshCw className="w-4 h-4 text-gray-500" />
            </button>
          </div>

          {/* 通知 */}
          <div className="relative">
            <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative">
              <Bell className="w-5 h-5 text-gray-600" />
              {notifications > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {notifications}
                </span>
              )}
            </button>
          </div>

          {/* 导出按钮 */}
          <Button
            variant="secondary"
            size="sm"
            onClick={handleExport}
            className="flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>导出</span>
          </Button>

          {/* 用户头像 */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-gray-600" />
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
