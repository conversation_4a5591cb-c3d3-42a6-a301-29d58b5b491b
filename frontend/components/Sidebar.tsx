'use client'

import { useState } from 'react'
import { 
  BarChart3, 
  Search, 
  Bot, 
  Upload, 
  Settings, 
  ChevronLeft,
  ChevronRight,
  Database,
  FileText,
  TrendingUp
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SidebarProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

const navigation = [
  {
    id: 'analysis',
    name: '在线分析',
    icon: BarChart3,
    description: '爬取并分析年报数据'
  },
  {
    id: 'keyword',
    name: '本地分析',
    icon: Search,
    description: '分析本地数据库中的年报'
  },
  {
    id: 'ai',
    name: 'AI 分析',
    icon: Bot,
    description: '智能分析年报内容'
  },
  {
    id: 'import',
    name: '数据导入',
    icon: Upload,
    description: '导入TXT文件到数据库'
  }
]

const stats = [
  { name: '已分析公司', value: '156', icon: Database },
  { name: '年报文件', value: '324', icon: FileText },
  { name: '关键词匹配', value: '1,247', icon: TrendingUp },
]

export function Sidebar({ activeTab, onTabChange }: SidebarProps) {
  const [collapsed, setCollapsed] = useState(false)

  return (
    <div className={cn(
      'flex flex-col bg-white border-r border-gray-200 transition-all duration-300',
      collapsed ? 'w-16' : 'w-64'
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!collapsed && (
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-black rounded-lg">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-sm font-semibold text-gray-900">
                年报分析工具
              </h1>
              <p className="text-xs text-gray-500">
                巨潮资讯网数据
              </p>
            </div>
          </div>
        )}
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
        >
          {collapsed ? (
            <ChevronRight className="w-4 h-4 text-gray-500" />
          ) : (
            <ChevronLeft className="w-4 h-4 text-gray-500" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon
          const isActive = activeTab === item.id
          
          return (
            <button
              key={item.id}
              onClick={() => onTabChange(item.id)}
              className={cn(
                'w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200',
                isActive
                  ? 'bg-gray-100 text-gray-900'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              )}
              title={collapsed ? item.name : undefined}
            >
              <Icon className="w-5 h-5 flex-shrink-0" />
              {!collapsed && (
                <div className="flex-1 text-left">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-xs text-gray-500 mt-0.5">
                    {item.description}
                  </div>
                </div>
              )}
            </button>
          )
        })}
      </nav>

      {/* Stats */}
      {!collapsed && (
        <div className="p-4 border-t border-gray-200">
          <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3">
            统计信息
          </h3>
          <div className="space-y-3">
            {stats.map((stat) => {
              const Icon = stat.icon
              return (
                <div key={stat.name} className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-lg">
                    <Icon className="w-4 h-4 text-gray-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {stat.value}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {stat.name}
                    </p>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Settings */}
      <div className="p-4 border-t border-gray-200">
        <button
          className={cn(
            'w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors',
            collapsed && 'justify-center'
          )}
          title={collapsed ? '设置' : undefined}
        >
          <Settings className="w-5 h-5 flex-shrink-0" />
          {!collapsed && <span>设置</span>}
        </button>
      </div>
    </div>
  )
}
