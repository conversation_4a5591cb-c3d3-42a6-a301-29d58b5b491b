#!/usr/bin/env python3
"""
测试上下文API功能
"""
import requests
import json

def test_context_api():
    """测试上下文API"""
    base_url = "http://localhost:5000/api"
    
    print("🔍 测试上下文API功能...")
    
    # 测试参数
    analysis_id = "keyword_analysis_test"
    keyword = "人工智能"
    
    # 构建URL
    url = f"{base_url}/keyword_context/{analysis_id}/{keyword}"
    params = {
        'context_length': 200,
        'stock_code': '000001',
        'file_name': '测试文件.txt'
    }
    
    print(f"📡 请求URL: {url}")
    print(f"📋 请求参数: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"📥 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 响应成功:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 响应失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器")
        print("   请确保后端服务器已启动")
    except Exception as e:
        print(f"❌ 请求错误: {e}")

if __name__ == "__main__":
    test_context_api()
